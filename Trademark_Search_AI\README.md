# 🔍 Modern AI Trademark Search

> **A modernized, production-ready trademark search system powered by advanced AI models**

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Streamlit](https://img.shields.io/badge/streamlit-1.40+-red.svg)](https://streamlit.io/)
[![<PERSON><PERSON><PERSON><PERSON>](https://img.shields.io/badge/langchain-0.3+-green.svg)](https://langchain.com/)

## 🚀 Quick Start

```bash
# Clone the repository
git clone https://github.com/davemadsen11/Trademark_Search_AI.git
cd Trademark_Search_AI

# Run the automated setup
python setup.py

# Start the application
streamlit run modern_app.py
```

## ✨ Features

### 🤖 **Multiple AI Model Support**
- **Local Models** (via Ollama): Llama 3.1, Qwen2.5, Mi<PERSON><PERSON>, Phi3
- **Cloud Models**: OpenAI GPT-4, <PERSON><PERSON><PERSON>, Together AI
- **Easy Model Switching** in the UI

### 🔍 **Advanced Search Capabilities**
- **Intelligent Filtering** with stopword removal
- **Vector Similarity Search** using state-of-the-art embeddings
- **Professional Legal Analysis** with conflict assessment
- **Real-time Results** with detailed explanations

### 🎨 **Modern User Interface**
- **Clean, Professional Design** built with Streamlit
- **Interactive Model Selection**
- **Search History** tracking
- **Responsive Layout** for all devices

### 📊 **Enhanced Data Pipeline**
- **Automated Data Processing** from USPTO sources
- **Sample Data Generation** for testing
- **Flexible Data Management**

## 🛠️ Installation

### Prerequisites
- Python 3.8 or higher
- (Optional) [Ollama](https://ollama.ai) for local models

### Automated Setup
```bash
python setup.py
```

### Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Setup environment
cp .env.example .env
# Edit .env with your API keys

# Generate sample data
python data_pipeline.py

# Start the application
streamlit run modern_app.py
```

## 🔧 Configuration

### Environment Variables
Create a `.env` file with your configuration:

```env
# Model Configuration
DEFAULT_MODEL_PROVIDER=ollama
DEFAULT_MODEL_NAME=llama3.1:8b

# API Keys (optional)
OPENAI_API_KEY=your_key_here
GROQ_API_KEY=your_key_here

# Embedding Model
EMBEDDING_MODEL=sentence-transformers/all-mpnet-base-v2
```

### Supported Models

#### 🏠 Local Models (Ollama)
- `llama3.1:8b` - Best overall performance
- `llama3.1:3b` - Faster, good quality
- `qwen2.5:7b` - Excellent reasoning
- `mistral:7b` - Balanced performance

#### ☁️ Cloud Models
- **OpenAI**: GPT-4o, GPT-4-turbo, GPT-3.5-turbo
- **Groq**: Llama 3.1 (ultra-fast inference)
- **Together AI**: Various open-source models

## 📖 Usage

### Web Interface
1. Start the application: `streamlit run modern_app.py`
2. Select your preferred AI model from the sidebar
3. Enter a trademark name to search
4. Review the detailed analysis and recommendations

### Programmatic Usage
```python
from modern_rag import ModernTrademarkRAG

# Initialize with local model
rag = ModernTrademarkRAG(
    model_provider="ollama",
    model_name="llama3.1:8b"
)

# Search for trademark conflicts
result = rag.search_trademarks("MyBrandName")
print(result)
```

## 🏗️ Architecture

### System Components
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Streamlit UI  │────│  Modern RAG      │────│  Vector Store   │
│                 │    │  Engine          │    │  (ChromaDB)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                       ┌────────┴────────┐
                       │   AI Models     │
                       │ • Ollama        │
                       │ • OpenAI        │
                       │ • Groq          │
                       └─────────────────┘
```

### Key Improvements Over Original
- ✅ **Updated Dependencies**: Latest LangChain, modern imports
- ✅ **Multiple Model Support**: Local and cloud options
- ✅ **Better Error Handling**: Robust exception management
- ✅ **Enhanced UI**: Professional design with configuration options
- ✅ **Improved Embeddings**: State-of-the-art embedding models
- ✅ **Data Pipeline**: Automated data processing and management
- ✅ **Configuration Management**: Environment-based settings

## 📊 Data Sources

### USPTO Trademark Database
The system processes data from the United States Patent and Trademark Office:
- **Case Files**: Serial numbers, trademark names, status codes
- **Classifications**: Industry categories and usage dates
- **Statements**: Descriptions of goods and services
- **Status Codes**: Live/Dead/Cancelled trademark statuses

### Sample Data
For demonstration purposes, the system includes sample trademark data featuring major tech companies. For production use, integrate with the full USPTO database.

## 🔍 How It Works

### 1. **Data Processing**
```python
# Automated data pipeline
pipeline = USPTODataPipeline()
pipeline.download_and_process()
```

### 2. **Intelligent Filtering**
- Removes stopwords in multiple languages
- Uses regex pattern matching for precise results
- Handles special characters and edge cases

### 3. **Vector Search**
- Converts trademark data to embeddings
- Performs semantic similarity search
- Retrieves most relevant potential conflicts

### 4. **AI Analysis**
- Professional legal reasoning
- Considers trademark law principles
- Provides clear recommendations

## 🚀 Performance

### Model Comparison
| Model | Speed | Quality | Cost | Best For |
|-------|-------|---------|------|----------|
| Llama 3.1 8B (Ollama) | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Free | Production |
| Qwen2.5 7B (Ollama) | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | Free | Reasoning |
| GPT-4o (OpenAI) | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | $$$ | Premium |
| Llama 3.1 (Groq) | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | $ | Speed |

### Embedding Models
- **all-mpnet-base-v2**: Best overall quality
- **bge-base-en-v1.5**: Excellent for legal text
- **all-MiniLM-L12-v2**: Good balance of speed/quality

## 🛡️ Legal Disclaimer

⚠️ **Important**: This tool provides preliminary trademark analysis for informational purposes only. It does not constitute legal advice. Always consult with a qualified trademark attorney for:

- Professional trademark searches
- Legal opinions on registrability
- Trademark application filing
- Conflict resolution strategies

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes
4. **Test** thoroughly
5. **Submit** a pull request

### Development Setup
```bash
# Install development dependencies
pip install -r requirements.txt
pip install pytest black flake8

# Run tests
pytest

# Format code
black .

# Lint code
flake8 .
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Original Project**: Built upon the foundation by [davemadsen11](https://github.com/davemadsen11)
- **USPTO**: For providing public trademark data
- **LangChain**: For the excellent RAG framework
- **Streamlit**: For the amazing web framework
- **HuggingFace**: For state-of-the-art embedding models

## 📞 Support

- **Issues**: [GitHub Issues](https://github.com/davemadsen11/Trademark_Search_AI/issues)
- **Discussions**: [GitHub Discussions](https://github.com/davemadsen11/Trademark_Search_AI/discussions)
- **Documentation**: See the `/docs` folder for detailed guides

---

<div align="center">
  <p><strong>Built with ❤️ for the open source community</strong></p>
  <p>⭐ Star this repo if you find it useful!</p>
</div>

