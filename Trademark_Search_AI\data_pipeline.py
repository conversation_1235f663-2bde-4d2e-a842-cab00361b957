"""Data pipeline for downloading and processing USPTO trademark data."""

import os
import pandas as pd
import requests
import zipfile
from pathlib import Path
import logging
from typing import Optional, List
from tqdm import tqdm
import tempfile
import shutil

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class USPTODataPipeline:
    """Pipeline for downloading and processing USPTO trademark data."""
    
    def __init__(self, data_dir: str = "data"):
        """Initialize the data pipeline."""
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # USPTO data URLs (these may need to be updated)
        self.base_url = "https://www.uspto.gov/sites/default/files/documents"
        self.data_files = {
            "case_file": "case_file.csv",
            "statement": "statement.csv", 
            "classification": "classification.csv",
            "status_codes": "status_codes.xlsx"
        }
        
        logger.info(f"Initialized data pipeline with data directory: {self.data_dir}")
    
    def download_file(self, url: str, filename: str, force_download: bool = False) -> bool:
        """Download a file from URL if it doesn't exist locally."""
        file_path = self.data_dir / filename
        
        if file_path.exists() and not force_download:
            logger.info(f"File already exists: {filename}")
            return True
        
        try:
            logger.info(f"Downloading {filename} from {url}")
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            
            with open(file_path, 'wb') as file, tqdm(
                desc=filename,
                total=total_size,
                unit='B',
                unit_scale=True,
                unit_divisor=1024,
            ) as pbar:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        file.write(chunk)
                        pbar.update(len(chunk))
            
            logger.info(f"Successfully downloaded: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to download {filename}: {e}")
            return False
    
    def create_sample_data(self) -> bool:
        """Create sample trademark data for testing purposes."""
        logger.info("Creating sample trademark data...")
        
        try:
            # Sample case file data
            case_data = {
                'serial_no': [88000001, 88000002, 88000003, 88000004, 88000005],
                'mark_id_char': ['APPLE', 'GOOGLE', 'MICROSOFT', 'AMAZON', 'FACEBOOK'],
                'cfh_status_cd': [1, 1, 1, 1, 1],
                'cfh_status_dt': ['2020-01-01', '2020-01-02', '2020-01-03', '2020-01-04', '2020-01-05']
            }
            
            # Sample statement data
            statement_data = {
                'serial_no': [88000001, 88000002, 88000003, 88000004, 88000005],
                'statement_type_cd': [1, 1, 1, 1, 1],
                'statement_text': [
                    'Computer hardware and software',
                    'Search engine services',
                    'Computer software and operating systems',
                    'Online retail services',
                    'Social networking services'
                ]
            }
            
            # Sample classification data
            classification_data = {
                'serial_no': [88000001, 88000002, 88000003, 88000004, 88000005],
                'first_use_any_dt': ['1980-01-01', '1998-01-01', '1985-01-01', '1995-01-01', '2004-01-01'],
                'first_use_com_dt': ['1980-01-01', '1998-01-01', '1985-01-01', '1995-01-01', '2004-01-01']
            }
            
            # Sample status codes
            status_data = {
                'Code': [1, 2, 3, 4, 5],
                'Live/Dead/Indifferent': ['LIVE', 'DEAD', 'CANCELLED', 'LIVE', 'LIVE'],
                'Code Definition': [
                    'Registered',
                    'Abandoned',
                    'Cancelled',
                    'Published for Opposition',
                    'Notice of Allowance'
                ]
            }
            
            # Save sample data
            pd.DataFrame(case_data).to_csv(self.data_dir / 'case_file.csv', index=False)
            pd.DataFrame(statement_data).to_csv(self.data_dir / 'statement.csv', index=False)
            pd.DataFrame(classification_data).to_csv(self.data_dir / 'classification.csv', index=False)
            pd.DataFrame(status_data).to_excel(self.data_dir / 'status_codes.xlsx', index=False)
            
            logger.info("Sample data created successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create sample data: {e}")
            return False
    
    def process_data(self, use_sample: bool = False) -> bool:
        """Process the downloaded data and create the final pickle file."""
        logger.info("Processing trademark data...")
        
        try:
            if use_sample:
                self.create_sample_data()
            
            # Load the data files
            case_df = pd.read_csv(self.data_dir / 'case_file.csv')
            statement_df = pd.read_csv(self.data_dir / 'statement.csv')
            classification_df = pd.read_csv(self.data_dir / 'classification.csv')
            status_code_df = pd.read_excel(self.data_dir / 'status_codes.xlsx')
            
            logger.info(f"Loaded data: case_file={len(case_df)}, statement={len(statement_df)}, "
                       f"classification={len(classification_df)}, status_codes={len(status_code_df)}")
            
            # Merge the dataframes
            df = pd.merge(case_df, statement_df, on='serial_no', how='left')
            df = pd.merge(df, classification_df, on='serial_no', how='left')
            df = pd.merge(df, status_code_df, left_on='cfh_status_cd', right_on='Code', how='left')
            
            # Clean the data
            df.drop_duplicates(inplace=True)
            df.dropna(subset=['mark_id_char'], inplace=True)
            
            # Rename columns to be more user-friendly
            column_mapping = {
                'serial_no': 'serial_number',
                'mark_id_char': 'trademarked_name',
                'cfh_status_cd': 'casefile_status_code',
                'cfh_status_dt': 'casefile_status_date',
                'statement_type_cd': 'statement_type_code',
                'statement_text': 'trademark_description',
                'first_use_any_dt': 'first_use_date',
                'first_use_com_dt': 'first_use_com_date',
                'Live/Dead/Indifferent': 'trademark_status',
                'Code Definition': 'status_definition'
            }
            
            df = df.rename(columns=column_mapping)
            
            # Save the processed data
            output_path = self.data_dir / 'trademark_data.pkl'
            df.to_pickle(output_path)
            
            logger.info(f"Successfully processed {len(df)} trademark records")
            logger.info(f"Saved processed data to: {output_path}")
            
            # Display sample of the data
            logger.info("Sample of processed data:")
            logger.info(df.head().to_string())
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to process data: {e}")
            return False
    
    def download_and_process(self, use_sample: bool = True, force_download: bool = False) -> bool:
        """Download and process all USPTO data."""
        logger.info("Starting USPTO data download and processing...")
        
        if use_sample:
            logger.info("Using sample data for demonstration")
            return self.process_data(use_sample=True)
        
        # For real data, you would implement the actual download URLs
        logger.warning("Real USPTO data download not implemented. Using sample data instead.")
        return self.process_data(use_sample=True)


def main():
    """Main function to run the data pipeline."""
    pipeline = USPTODataPipeline()
    
    # For now, use sample data since real USPTO URLs need to be researched
    success = pipeline.download_and_process(use_sample=True)
    
    if success:
        print("✅ Data pipeline completed successfully!")
        print(f"📁 Data saved to: {pipeline.data_dir / 'trademark_data.pkl'}")
    else:
        print("❌ Data pipeline failed!")


if __name__ == "__main__":
    main()
