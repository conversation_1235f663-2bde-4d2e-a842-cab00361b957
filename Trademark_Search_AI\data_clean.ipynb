{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["case_df = pd.read_csv('csv/case_file.csv')\n", "statement_df = pd.read_csv('csv/statement.csv')\n", "classification_df = pd.read_csv('csv/classification.csv')\n", "status_code_df = pd.read_excel('csv/status_codes.xlsx')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.merge(case_df, statement_df, on='serial_no', how='left')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.merge(df, classification_df, on='serial_no', how='left')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = pd.merge(df, status_code_df, left_on='cfh_status_cd', right_on='Code', how='left')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Free up memory\n", "lst = [case_df, statement_df, classification_df, status_code_df]\n", "del case_df, statement_df, classification_df, status_code_df\n", "del lst"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## Remove duplicates and nulls in the \"mark_id_char\" column\n", "\n", "df.drop_duplicates(inplace=True)\n", "\n", "df['serial_no'].value_counts()\n", "\n", "df.dropna(subset='mark_id_char',inplace=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Define the original and new column names\n", "original_columns = ['serial_no', 'mark_id_char', 'cfh_status_cd', 'cfh_status_dt',\n", "                    'statement_type_cd', 'statement_text', 'first_use_any_dt',\n", "                    'first_use_com_dt', 'Code', 'Live/Dead/Indifferent', 'Code Definition']\n", "\n", "new_columns = ['serial_number', 'trademarked_name', 'casefile_status_code', 'casefile_status_date',\n", "               'statement_type_code', 'trademark_description/statement', 'first_use_date',\n", "               'first_use_com_date', 'Code', 'Trademark_Status', 'Code_Definition']\n", "\n", "# Create a dictionary mapping the original column names to the new column names\n", "column_mapping = dict(zip(original_columns, new_columns))\n", "\n", "# Rename the columns in the DataFrame\n", "df = df.rename(columns=column_mapping)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df.to_pickle('df.pkl')"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}