# Core LangChain and AI dependencies
langchain>=0.3.0
langchain-community>=0.3.0
langchain-core>=0.3.0
langchain-openai>=0.2.0
langchain-ollama>=0.2.0
langchain-groq>=0.2.0
langchain-chroma>=0.1.0

# Vector stores and embeddings
chromadb>=0.5.0
sentence-transformers>=3.0.0
transformers>=4.40.0
torch>=2.0.0

# Web framework and UI
streamlit>=1.35.0
streamlit-chat>=0.1.0

# Data processing
pandas>=2.0.0
numpy>=1.24.0
nltk>=3.8.0

# HTTP clients for API calls
httpx>=0.25.0
requests>=2.30.0

# Environment and configuration
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.0.0

# Optional: Additional model providers
openai>=1.30.0
groq>=0.10.0

# Development and testing
pytest>=8.0.0
black>=24.0.0
flake8>=7.0.0

# Data download utilities
tqdm>=4.65.0
beautifulsoup4>=4.12.0
lxml>=5.0.0
