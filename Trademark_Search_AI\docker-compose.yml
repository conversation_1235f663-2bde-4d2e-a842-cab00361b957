version: '3.8'

services:
  trademark-ai:
    build: .
    ports:
      - "8501:8501"
    environment:
      - DEFAULT_MODEL_PROVIDER=ollama
      - DEFAULT_MODEL_NAME=llama3.1:8b
      - EMBEDDING_MODEL=sentence-transformers/all-mpnet-base-v2
    volumes:
      - ./data:/app/data
      - ./chroma_db:/app/chroma_db
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: Include Ollama service for local models
  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    restart: unless-stopped
    environment:
      - OLLAMA_HOST=0.0.0.0

volumes:
  ollama_data:
