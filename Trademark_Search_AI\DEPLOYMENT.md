# 🚀 Deployment Guide

This guide covers different deployment options for the Modern AI Trademark Search application.

## 📋 Prerequisites

- Python 3.8+ installed
- Git installed
- (Optional) Docker for containerized deployment
- (Optional) Ollama for local AI models

## 🏠 Local Development Setup

### 1. <PERSON>lone and Setup
```bash
git clone https://github.com/davemadsen11/Trademark_Search_AI.git
cd Trademark_Search_AI

# Run automated setup
python setup.py

# Or manual setup
pip install -r requirements.txt
cp .env.example .env
python data_pipeline.py
```

### 2. Configure Environment
Edit `.env` file with your preferences:
```env
DEFAULT_MODEL_PROVIDER=ollama
DEFAULT_MODEL_NAME=llama3.1:8b
OPENAI_API_KEY=your_key_here  # Optional
GROQ_API_KEY=your_key_here    # Optional
```

### 3. Install Local Models (Optional)
```bash
# Install Ollama from https://ollama.ai
ollama pull llama3.1:8b
ollama pull qwen2.5:7b
ollama pull mistral:7b
```

### 4. Start the Application
```bash
python -m streamlit run modern_app.py
```

## 🐳 Docker Deployment

### Quick Start with Docker Compose
```bash
# Start the application with Ollama
docker-compose up -d

# Access at http://localhost:8501
```

### Manual Docker Build
```bash
# Build the image
docker build -t trademark-ai .

# Run the container
docker run -p 8501:8501 \
  -e DEFAULT_MODEL_PROVIDER=openai \
  -e OPENAI_API_KEY=your_key_here \
  trademark-ai
```

## ☁️ Cloud Deployment

### Streamlit Cloud
1. Fork the repository on GitHub
2. Connect to [Streamlit Cloud](https://streamlit.io/cloud)
3. Deploy from your fork
4. Add secrets in Streamlit Cloud dashboard:
   - `OPENAI_API_KEY`
   - `GROQ_API_KEY`

### Heroku
```bash
# Install Heroku CLI
heroku create your-app-name
heroku config:set OPENAI_API_KEY=your_key_here
git push heroku main
```

### Railway
1. Connect GitHub repository to [Railway](https://railway.app)
2. Add environment variables
3. Deploy automatically

### Google Cloud Run
```bash
# Build and deploy
gcloud run deploy trademark-ai \
  --source . \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated
```

## 🔧 Configuration Options

### Model Providers

#### Local Models (Ollama)
- **Pros**: Free, private, no API limits
- **Cons**: Requires local installation, slower on CPU
- **Best for**: Development, privacy-sensitive use

#### OpenAI
- **Pros**: High quality, fast, reliable
- **Cons**: Costs money, requires API key
- **Best for**: Production, high-quality results

#### Groq
- **Pros**: Very fast inference, free tier
- **Cons**: Limited free usage, requires API key
- **Best for**: Speed-critical applications

### Environment Variables
```env
# Model Configuration
DEFAULT_MODEL_PROVIDER=ollama|openai|groq
DEFAULT_MODEL_NAME=llama3.1:8b|gpt-4o|llama-3.1-8b-instant

# API Keys
OPENAI_API_KEY=sk-...
GROQ_API_KEY=gsk_...

# Performance Tuning
RETRIEVAL_K=5
CHUNK_SIZE=512
EMBEDDING_DEVICE=cpu|cuda
```

## 📊 Performance Optimization

### For CPU-Only Deployment
```env
EMBEDDING_DEVICE=cpu
DEFAULT_MODEL_NAME=llama3.1:3b  # Smaller model
CHUNK_SIZE=256                  # Smaller chunks
```

### For GPU Deployment
```env
EMBEDDING_DEVICE=cuda
DEFAULT_MODEL_NAME=llama3.1:8b  # Larger model
CHUNK_SIZE=1024                 # Larger chunks
```

## 🔒 Security Considerations

### API Key Management
- Never commit API keys to version control
- Use environment variables or secret management
- Rotate keys regularly

### Data Privacy
- Sample data is included for demonstration
- For production, ensure compliance with data regulations
- Consider using local models for sensitive data

### Network Security
- Use HTTPS in production
- Implement rate limiting
- Add authentication if needed

## 📈 Monitoring and Logging

### Application Logs
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)
```

### Health Checks
```bash
# Check application health
curl http://localhost:8501/_stcore/health
```

### Performance Monitoring
- Monitor response times
- Track model usage and costs
- Monitor memory and CPU usage

## 🔄 Updates and Maintenance

### Updating Dependencies
```bash
pip install -r requirements.txt --upgrade
```

### Updating Models
```bash
# Update Ollama models
ollama pull llama3.1:8b

# Check for new model versions in config.py
```

### Data Updates
```bash
# Regenerate sample data
python data_pipeline.py

# For production: implement automated USPTO data updates
```

## 🆘 Troubleshooting

### Common Issues

#### "Module not found" errors
```bash
pip install -r requirements.txt
```

#### Ollama connection issues
```bash
# Check Ollama is running
ollama list

# Restart Ollama service
ollama serve
```

#### Memory issues
```bash
# Use smaller models
export DEFAULT_MODEL_NAME=llama3.1:3b

# Reduce chunk size
export CHUNK_SIZE=256
```

#### Slow performance
- Use GPU if available
- Switch to cloud models (Groq for speed)
- Reduce retrieval_k parameter

### Getting Help
- Check the [GitHub Issues](https://github.com/davemadsen11/Trademark_Search_AI/issues)
- Review application logs
- Test with `python test_system.py`

## 📞 Support

For deployment assistance:
1. Run the test suite: `python test_system.py`
2. Check the logs for specific error messages
3. Consult the troubleshooting section above
4. Open an issue on GitHub with deployment details

---

**Happy Deploying! 🚀**
