"""Setup script for the Trademark Search AI application."""

import subprocess
import sys
import os
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_command(command, description):
    """Run a command and handle errors."""
    logger.info(f"Running: {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ {description} failed: {e}")
        logger.error(f"Error output: {e.stderr}")
        return False


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        logger.error("❌ Python 3.8 or higher is required")
        return False
    logger.info(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def install_requirements():
    """Install Python requirements."""
    if not Path("requirements.txt").exists():
        logger.error("❌ requirements.txt not found")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing Python dependencies"
    )


def setup_nltk_data():
    """Download required NLTK data."""
    try:
        import nltk
        logger.info("Downloading NLTK stopwords data...")
        nltk.download('stopwords', quiet=True)
        logger.info("✅ NLTK data downloaded successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to download NLTK data: {e}")
        return False


def setup_environment():
    """Setup environment configuration."""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_example.exists() and not env_file.exists():
        try:
            import shutil
            shutil.copy(env_example, env_file)
            logger.info("✅ Created .env file from template")
            logger.info("📝 Please edit .env file to add your API keys if needed")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to create .env file: {e}")
            return False
    elif env_file.exists():
        logger.info("✅ .env file already exists")
        return True
    else:
        logger.warning("⚠️ No .env.example file found")
        return True


def setup_data_directory():
    """Create data directory."""
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    logger.info("✅ Data directory created")
    return True


def check_ollama():
    """Check if Ollama is installed and suggest models."""
    try:
        result = subprocess.run("ollama --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ Ollama is installed")
            logger.info("💡 Recommended models to pull:")
            logger.info("   ollama pull llama3.1:8b")
            logger.info("   ollama pull llama3.1:3b")
            logger.info("   ollama pull qwen2.5:7b")
            return True
        else:
            logger.warning("⚠️ Ollama not found. Install from https://ollama.ai for local models")
            return False
    except Exception:
        logger.warning("⚠️ Ollama not found. Install from https://ollama.ai for local models")
        return False


def run_data_pipeline():
    """Run the data pipeline to create sample data."""
    try:
        logger.info("Setting up sample trademark data...")
        from data_pipeline import USPTODataPipeline
        
        pipeline = USPTODataPipeline()
        success = pipeline.download_and_process(use_sample=True)
        
        if success:
            logger.info("✅ Sample trademark data created successfully")
            return True
        else:
            logger.error("❌ Failed to create sample data")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error running data pipeline: {e}")
        return False


def main():
    """Main setup function."""
    logger.info("🚀 Starting Trademark Search AI setup...")
    
    steps = [
        ("Checking Python version", check_python_version),
        ("Installing requirements", install_requirements),
        ("Setting up NLTK data", setup_nltk_data),
        ("Setting up environment", setup_environment),
        ("Creating data directory", setup_data_directory),
        ("Checking Ollama installation", check_ollama),
        ("Setting up sample data", run_data_pipeline),
    ]
    
    failed_steps = []
    
    for description, func in steps:
        if not func():
            failed_steps.append(description)
    
    if failed_steps:
        logger.error("❌ Setup completed with errors:")
        for step in failed_steps:
            logger.error(f"   - {step}")
        logger.info("\n📋 Next steps:")
        logger.info("1. Fix the failed steps above")
        logger.info("2. Edit .env file with your API keys (if using cloud models)")
        logger.info("3. Run: streamlit run modern_app.py")
    else:
        logger.info("✅ Setup completed successfully!")
        logger.info("\n🎉 You're ready to go!")
        logger.info("\n📋 Next steps:")
        logger.info("1. Edit .env file with your API keys (if using cloud models)")
        logger.info("2. Install Ollama and pull models (for local inference)")
        logger.info("3. Run: streamlit run modern_app.py")
        
        # Show available commands
        logger.info("\n🔧 Available commands:")
        logger.info("   streamlit run modern_app.py          # Start the web application")
        logger.info("   python data_pipeline.py             # Regenerate sample data")
        logger.info("   python -c 'from modern_rag import *' # Test the RAG system")


if __name__ == "__main__":
    main()
