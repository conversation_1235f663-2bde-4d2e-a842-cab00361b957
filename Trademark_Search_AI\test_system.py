"""Test script to verify the modernized trademark search system."""

import os
import sys
import logging
from pathlib import Path
import traceback

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_imports():
    """Test that all required modules can be imported."""
    logger.info("Testing imports...")
    
    try:
        # Test core imports
        import pandas as pd
        import numpy as np
        import streamlit as st
        logger.info("✅ Core libraries imported successfully")
        
        # Test LangChain imports
        from langchain_core.prompts import PromptTemplate
        from langchain_community.embeddings import HuggingFaceEmbeddings
        from langchain_chroma import Chroma
        logger.info("✅ LangChain libraries imported successfully")
        
        # Test our custom modules
        from config import settings, MODEL_CONFIGS
        from modern_rag import ModernTrademarkRAG
        from data_pipeline import USPTODataPipeline
        logger.info("✅ Custom modules imported successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False


def test_data_pipeline():
    """Test the data pipeline functionality."""
    logger.info("Testing data pipeline...")
    
    try:
        from data_pipeline import USPTODataPipeline
        
        pipeline = USPTODataPipeline()
        success = pipeline.download_and_process(use_sample=True)
        
        if success:
            # Check if data file exists
            data_file = Path("data/trademark_data.pkl")
            if data_file.exists():
                logger.info("✅ Data pipeline test passed")
                return True
            else:
                logger.error("❌ Data file not created")
                return False
        else:
            logger.error("❌ Data pipeline failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Data pipeline test failed: {e}")
        traceback.print_exc()
        return False


def test_rag_system():
    """Test the RAG system with sample data."""
    logger.info("Testing RAG system...")
    
    try:
        from modern_rag import ModernTrademarkRAG
        
        # Test with a mock model (no actual LLM needed for basic functionality test)
        logger.info("Testing RAG system initialization...")
        
        # Check if we can load the data
        import pandas as pd
        data_file = Path("data/trademark_data.pkl")
        
        if not data_file.exists():
            logger.error("❌ No data file found for RAG testing")
            return False
        
        df = pd.read_pickle(data_file)
        logger.info(f"✅ Loaded {len(df)} trademark records")
        
        # Test filtering functionality
        from modern_rag import ModernTrademarkRAG
        rag = ModernTrademarkRAG.__new__(ModernTrademarkRAG)  # Create without __init__
        rag.df = df
        
        filtered_df = rag.filter_trademarks(df, "APPLE")
        if len(filtered_df) > 0:
            logger.info(f"✅ Filtering test passed: found {len(filtered_df)} matches for 'APPLE'")
        else:
            logger.warning("⚠️ No matches found for 'APPLE' - this might be expected")
        
        logger.info("✅ RAG system basic functionality test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ RAG system test failed: {e}")
        traceback.print_exc()
        return False


def test_embeddings():
    """Test embedding model functionality."""
    logger.info("Testing embeddings...")
    
    try:
        from langchain_community.embeddings import HuggingFaceEmbeddings
        
        # Test with a lightweight model
        embeddings = HuggingFaceEmbeddings(
            model_name="sentence-transformers/all-MiniLM-L6-v2",
            model_kwargs={'device': 'cpu'}
        )
        
        # Test embedding generation
        test_text = "This is a test trademark description"
        embedding = embeddings.embed_query(test_text)
        
        if len(embedding) > 0:
            logger.info(f"✅ Embeddings test passed: generated {len(embedding)}-dimensional vector")
            return True
        else:
            logger.error("❌ Empty embedding generated")
            return False
            
    except Exception as e:
        logger.error(f"❌ Embeddings test failed: {e}")
        traceback.print_exc()
        return False


def test_configuration():
    """Test configuration system."""
    logger.info("Testing configuration...")
    
    try:
        from config import settings, MODEL_CONFIGS, EMBEDDING_MODELS
        
        # Test settings
        logger.info(f"Default model provider: {settings.default_model_provider}")
        logger.info(f"Default model name: {settings.default_model_name}")
        logger.info(f"Embedding model: {settings.embedding_model}")
        
        # Test model configs
        if len(MODEL_CONFIGS) > 0:
            logger.info(f"✅ Found {len(MODEL_CONFIGS)} model providers")
            for provider, models in MODEL_CONFIGS.items():
                logger.info(f"  - {provider}: {len(models)} models")
        else:
            logger.error("❌ No model configurations found")
            return False
        
        # Test embedding configs
        if len(EMBEDDING_MODELS) > 0:
            logger.info(f"✅ Found {len(EMBEDDING_MODELS)} embedding models")
        else:
            logger.error("❌ No embedding model configurations found")
            return False
        
        logger.info("✅ Configuration test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False


def test_streamlit_app():
    """Test that the Streamlit app can be imported without errors."""
    logger.info("Testing Streamlit app...")
    
    try:
        # Test that the app file exists and can be parsed
        app_file = Path("modern_app.py")
        if not app_file.exists():
            logger.error("❌ modern_app.py not found")
            return False
        
        # Try to compile the app file
        with open(app_file, 'r', encoding='utf-8') as f:
            app_code = f.read()
        
        compile(app_code, str(app_file), 'exec')
        logger.info("✅ Streamlit app syntax test passed")
        return True
        
    except Exception as e:
        logger.error(f"❌ Streamlit app test failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    logger.info("🧪 Starting comprehensive system tests...")
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Data Pipeline Test", test_data_pipeline),
        ("Embeddings Test", test_embeddings),
        ("RAG System Test", test_rag_system),
        ("Streamlit App Test", test_streamlit_app),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name} PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name} FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name} FAILED with exception: {e}")
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info(f"TEST SUMMARY")
    logger.info(f"{'='*50}")
    logger.info(f"✅ Passed: {passed}")
    logger.info(f"❌ Failed: {failed}")
    logger.info(f"📊 Success Rate: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        logger.info("🎉 All tests passed! The system is ready to use.")
        logger.info("\n📋 Next steps:")
        logger.info("1. Install Ollama for local models: https://ollama.ai")
        logger.info("2. Pull a model: ollama pull llama3.1:8b")
        logger.info("3. Add API keys to .env file for cloud models")
        logger.info("4. Start the app: python -m streamlit run modern_app.py")
    else:
        logger.error("❌ Some tests failed. Please fix the issues before proceeding.")
    
    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
