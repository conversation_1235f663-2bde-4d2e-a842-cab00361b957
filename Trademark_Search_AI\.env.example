# Environment Configuration for Trademark Search AI
# Copy this file to .env and fill in your API keys

# OpenAI Configuration (optional)
OPENAI_API_KEY=your_openai_api_key_here

# Groq Configuration (optional)
GROQ_API_KEY=your_groq_api_key_here

# Together AI Configuration (optional)
TOGETHER_API_KEY=your_together_api_key_here

# Model Configuration
DEFAULT_MODEL_PROVIDER=ollama
DEFAULT_MODEL_NAME=llama3.1:8b

# Embedding Configuration
EMBEDDING_MODEL=sentence-transformers/all-mpnet-base-v2
EMBEDDING_DEVICE=cpu

# Data Configuration
DATA_FILE_PATH=data/trademark_data.pkl

# Vector Store Configuration
VECTOR_STORE_PERSIST_DIRECTORY=./chroma_db
RETRIEVAL_K=5
CHUNK_SIZE=512
CHUNK_OVERLAP=64

# Application Configuration
APP_TITLE=AI Trademark Search
APP_ICON=⚖️
