"""Modern Streamlit application for trademark search with multiple model support."""

import streamlit as st
import os
import logging
from pathlib import Path
from typing import Dict, List, Optional
import time

# Import our modern components
from modern_rag import ModernTrademarkRAG, MODEL_CONFIGS
from config import settings
from data_pipeline import USPTODataPipeline

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title=settings.app_title,
    page_icon=settings.app_icon,
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .search-box {
        font-size: 1.2rem;
        padding: 1rem;
        border-radius: 10px;
        border: 2px solid #1f77b4;
    }
    .result-box {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 5px solid #1f77b4;
        margin: 1rem 0;
    }
    .warning-box {
        background-color: #fff3cd;
        padding: 1rem;
        border-radius: 5px;
        border-left: 5px solid #ffc107;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        padding: 1rem;
        border-radius: 5px;
        border-left: 5px solid #dc3545;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize Streamlit session state variables."""
    if "rag_system" not in st.session_state:
        st.session_state.rag_system = None
    if "search_history" not in st.session_state:
        st.session_state.search_history = []
    if "current_model" not in st.session_state:
        st.session_state.current_model = f"{settings.default_model_provider}:{settings.default_model_name}"
    if "repeat_search_term" not in st.session_state:
        st.session_state.repeat_search_term = None


def setup_sidebar():
    """Setup the sidebar with model selection and configuration."""
    st.sidebar.title("🔧 Configuration")
    
    # Model selection
    st.sidebar.subheader("Model Selection")
    
    available_models = {}
    for provider, models in MODEL_CONFIGS.items():
        for model in models.keys():
            available_models[f"{provider}:{model}"] = (provider, model)
    
    selected_model = st.sidebar.selectbox(
        "Choose AI Model",
        options=list(available_models.keys()),
        index=list(available_models.keys()).index(st.session_state.current_model) 
        if st.session_state.current_model in available_models else 0,
        help="Select the AI model for trademark analysis"
    )
    
    # API Key inputs (if needed)
    api_keys = {}
    provider, model_name = available_models[selected_model]
    
    if provider == "openai":
        api_keys['api_key'] = st.sidebar.text_input(
            "OpenAI API Key",
            type="password",
            value=os.getenv('OPENAI_API_KEY', ''),
            help="Enter your OpenAI API key"
        )
    elif provider == "groq":
        api_keys['api_key'] = st.sidebar.text_input(
            "Groq API Key", 
            type="password",
            value=os.getenv('GROQ_API_KEY', ''),
            help="Enter your Groq API key"
        )
    
    # Data management
    st.sidebar.subheader("Data Management")
    
    data_file_exists = Path(settings.data_file_path).exists()
    
    if data_file_exists:
        st.sidebar.success("✅ Trademark data loaded")
        if st.sidebar.button("🔄 Refresh Data"):
            setup_data_pipeline()
    else:
        st.sidebar.warning("⚠️ No trademark data found")
        if st.sidebar.button("📥 Download Sample Data"):
            setup_data_pipeline()
    
    # Advanced settings
    with st.sidebar.expander("⚙️ Advanced Settings"):
        retrieval_k = st.slider("Number of results to retrieve", 3, 20, settings.retrieval_k)
        chunk_size = st.slider("Text chunk size", 256, 1024, settings.chunk_size)
        
        if st.button("Apply Settings"):
            settings.retrieval_k = retrieval_k
            settings.chunk_size = chunk_size
            st.rerun()
    
    return selected_model, api_keys


def setup_data_pipeline():
    """Setup and run the data pipeline."""
    with st.spinner("Setting up trademark data..."):
        try:
            pipeline = USPTODataPipeline()
            success = pipeline.download_and_process(use_sample=True)
            
            if success:
                st.success("✅ Trademark data setup completed!")
                st.rerun()
            else:
                st.error("❌ Failed to setup trademark data")
                
        except Exception as e:
            st.error(f"❌ Error setting up data: {str(e)}")


def initialize_rag_system(model_selection: str, api_keys: Dict):
    """Initialize or update the RAG system."""
    provider, model_name = model_selection.split(":", 1)
    
    try:
        if (st.session_state.rag_system is None or 
            st.session_state.current_model != model_selection):
            
            with st.spinner(f"Initializing {provider} model: {model_name}..."):
                st.session_state.rag_system = ModernTrademarkRAG(
                    model_provider=provider,
                    model_name=model_name,
                    **api_keys
                )
                st.session_state.current_model = model_selection
                
        return True
        
    except Exception as e:
        st.error(f"❌ Failed to initialize model: {str(e)}")
        return False


def display_search_interface():
    """Display the main search interface."""
    st.markdown('<h1 class="main-header">🔍 AI Trademark Search</h1>', unsafe_allow_html=True)

    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <p style="font-size: 1.2rem; color: #666;">
            Check if your trademark is available for registration using AI-powered analysis
        </p>
    </div>
    """, unsafe_allow_html=True)

    # Handle repeat search from history
    default_value = ""
    if hasattr(st.session_state, 'repeat_search_term') and st.session_state.repeat_search_term:
        default_value = st.session_state.repeat_search_term
        # Clear the repeat search term after using it
        st.session_state.repeat_search_term = None

    # Search input
    col1, col2 = st.columns([4, 1])

    with col1:
        search_term = st.text_input(
            "Enter trademark name to search:",
            value=default_value,
            placeholder="e.g., MyBrand, TechCorp, etc.",
            key="search_input",
            label_visibility="collapsed"
        )

    with col2:
        search_button = st.button("🔍 Search", type="primary", use_container_width=True)

    return search_term, search_button


def display_search_results(search_term: str):
    """Display search results."""
    if not st.session_state.rag_system:
        st.error("❌ RAG system not initialized")
        return
    
    try:
        with st.spinner("🔍 Analyzing trademark database..."):
            start_time = time.time()
            result = st.session_state.rag_system.search_trademarks(search_term)
            end_time = time.time()
        
        # Display results
        st.markdown('<div class="result-box">', unsafe_allow_html=True)
        st.markdown(result)
        st.markdown('</div>', unsafe_allow_html=True)
        
        # Add to search history
        st.session_state.search_history.append({
            "term": search_term,
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "duration": f"{end_time - start_time:.2f}s",
            "model": st.session_state.current_model
        })
        
        # Keep only last 10 searches
        if len(st.session_state.search_history) > 10:
            st.session_state.search_history = st.session_state.search_history[-10:]
            
    except Exception as e:
        st.error(f"❌ Search failed: {str(e)}")


def display_search_history():
    """Display recent search history."""
    if st.session_state.search_history:
        st.subheader("📋 Recent Searches")

        for i, search in enumerate(reversed(st.session_state.search_history[-5:])):
            with st.expander(f"🔍 {search['term']} - {search['timestamp']}"):
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.write(f"**Model:** {search['model']}")
                with col2:
                    st.write(f"**Duration:** {search['duration']}")
                with col3:
                    if st.button("🔄 Search Again", key=f"repeat_{i}"):
                        # Store the term to search in session state for next run
                        st.session_state.repeat_search_term = search['term']
                        st.rerun()


def main():
    """Main application function."""
    initialize_session_state()
    
    # Setup sidebar
    selected_model, api_keys = setup_sidebar()
    
    # Initialize RAG system
    if not initialize_rag_system(selected_model, api_keys):
        st.stop()
    
    # Main interface
    search_term, search_button = display_search_interface()
    
    # Handle search
    if search_button and search_term.strip():
        display_search_results(search_term.strip())
    elif search_button and not search_term.strip():
        st.warning("⚠️ Please enter a trademark name to search")
    
    # Display search history
    display_search_history()
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style="text-align: center; color: #666; font-size: 0.9rem;">
        <p>⚠️ <strong>Disclaimer:</strong> This tool provides preliminary analysis only. 
        Consult with a qualified trademark attorney for professional legal advice.</p>
        <p>Powered by AI • Built with Streamlit</p>
    </div>
    """, unsafe_allow_html=True)


if __name__ == "__main__":
    main()
