"""Configuration settings for the Trademark Search AI application."""

import os
from typing import Dict, Any, Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings with environment variable support."""
    
    # Model configuration
    default_model_provider: str = Field(default="ollama", description="Default model provider")
    default_model_name: str = Field(default="llama3.1:8b", description="Default model name")
    
    # Embedding configuration
    embedding_model: str = Field(default="sentence-transformers/all-mpnet-base-v2", description="Embedding model")
    embedding_device: str = Field(default="cpu", description="Device for embeddings")
    
    # Vector store configuration
    vector_store_persist_directory: str = Field(default="./chroma_db", description="ChromaDB persist directory")
    retrieval_k: int = Field(default=5, description="Number of documents to retrieve")
    chunk_size: int = Field(default=512, description="Text chunk size")
    chunk_overlap: int = Field(default=64, description="Text chunk overlap")
    
    # API Keys (optional)
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    groq_api_key: Optional[str] = Field(default=None, description="Groq API key")
    together_api_key: Optional[str] = Field(default=None, description="Together AI API key")
    
    # Data configuration
    data_file_path: str = Field(default="data/trademark_data.pkl", description="Path to trademark data file")
    
    # Streamlit configuration
    app_title: str = Field(default="AI Trademark Search", description="Application title")
    app_icon: str = Field(default="⚖️", description="Application icon")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Model provider configurations
MODEL_CONFIGS: Dict[str, Dict[str, Any]] = {
    "ollama": {
        "llama3.1:8b": {"temperature": 0.1, "top_p": 0.9},
        "llama3.1:3b": {"temperature": 0.1, "top_p": 0.9},
        "llama3.2:3b": {"temperature": 0.1, "top_p": 0.9},
        "qwen2.5:7b": {"temperature": 0.1, "top_p": 0.9},
        "qwen2.5:3b": {"temperature": 0.1, "top_p": 0.9},
        "mistral:7b": {"temperature": 0.1, "top_p": 0.9},
        "phi3:3.8b": {"temperature": 0.1, "top_p": 0.9},
    },
    "openai": {
        "gpt-4o": {"temperature": 0.1, "max_tokens": 2000},
        "gpt-4o-mini": {"temperature": 0.1, "max_tokens": 2000},
        "gpt-4-turbo": {"temperature": 0.1, "max_tokens": 2000},
        "gpt-3.5-turbo": {"temperature": 0.1, "max_tokens": 2000},
    },
    "groq": {
        "llama-3.1-8b-instant": {"temperature": 0.1, "max_tokens": 2000},
        "llama-3.1-70b-versatile": {"temperature": 0.1, "max_tokens": 2000},
        "mixtral-8x7b-32768": {"temperature": 0.1, "max_tokens": 2000},
        "gemma2-9b-it": {"temperature": 0.1, "max_tokens": 2000},
    }
}

# Embedding model options
EMBEDDING_MODELS = {
    "all-mpnet-base-v2": "sentence-transformers/all-mpnet-base-v2",
    "all-MiniLM-L12-v2": "sentence-transformers/all-MiniLM-L12-v2", 
    "all-MiniLM-L6-v2": "sentence-transformers/all-MiniLM-L6-v2",
    "bge-small-en-v1.5": "BAAI/bge-small-en-v1.5",
    "bge-base-en-v1.5": "BAAI/bge-base-en-v1.5",
}

# Create global settings instance
settings = Settings()
