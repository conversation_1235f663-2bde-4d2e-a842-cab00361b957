"""Modern RAG implementation for trademark search with multiple model support."""

import os
import logging
from typing import Dict, List, Optional, Any, Union
import pandas as pd
import re
from pathlib import Path

# LangChain imports (updated)
from langchain_core.prompts import PromptTemplate
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_chroma import Chroma

# Model imports
from langchain_ollama import ChatOllama
from langchain_openai import ChatOpenAI
from langchain_groq import ChatGroq

# NLTK for stopwords
import nltk
from nltk.corpus import stopwords

# Configuration
from config import settings, MODEL_CONFIGS, EMBEDDING_MODELS

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Download NLTK data if needed
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')


class ModelFactory:
    """Factory class for creating different types of language models."""
    
    @staticmethod
    def create_model(provider: str, model_name: str, **kwargs) -> Any:
        """Create a language model based on provider and model name."""
        
        if provider == "ollama":
            return ChatOllama(
                model=model_name,
                **MODEL_CONFIGS.get(provider, {}).get(model_name, {}),
                **kwargs
            )
        
        elif provider == "openai":
            api_key = kwargs.get('api_key') or settings.openai_api_key or os.getenv('OPENAI_API_KEY')
            if not api_key:
                raise ValueError("OpenAI API key is required for OpenAI models")
            
            return ChatOpenAI(
                model=model_name,
                api_key=api_key,
                **MODEL_CONFIGS.get(provider, {}).get(model_name, {}),
                **kwargs
            )
        
        elif provider == "groq":
            api_key = kwargs.get('api_key') or settings.groq_api_key or os.getenv('GROQ_API_KEY')
            if not api_key:
                raise ValueError("Groq API key is required for Groq models")
            
            return ChatGroq(
                model=model_name,
                api_key=api_key,
                **MODEL_CONFIGS.get(provider, {}).get(model_name, {}),
                **kwargs
            )
        
        else:
            raise ValueError(f"Unsupported model provider: {provider}")


class ModernTrademarkRAG:
    """Modern RAG implementation for trademark search with enhanced features."""
    
    def __init__(
        self,
        model_provider: str = None,
        model_name: str = None,
        embedding_model: str = None,
        data_path: str = None,
        **model_kwargs
    ):
        """Initialize the trademark RAG system."""
        
        self.model_provider = model_provider or settings.default_model_provider
        self.model_name = model_name or settings.default_model_name
        self.embedding_model_name = embedding_model or settings.embedding_model
        self.data_path = data_path or settings.data_file_path
        
        # Initialize components
        self.model = None
        self.embeddings = None
        self.vector_store = None
        self.retriever = None
        self.chain = None
        self.df = None
        
        # Text processing
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap
        )
        
        # Initialize the system
        self._initialize_model(**model_kwargs)
        self._initialize_embeddings()
        self._load_data()
        self._setup_prompt()
        
        logger.info(f"Initialized ModernTrademarkRAG with {self.model_provider}:{self.model_name}")
    
    def _initialize_model(self, **kwargs):
        """Initialize the language model."""
        try:
            self.model = ModelFactory.create_model(
                self.model_provider, 
                self.model_name, 
                **kwargs
            )
            logger.info(f"Successfully initialized {self.model_provider} model: {self.model_name}")
        except Exception as e:
            logger.error(f"Failed to initialize model: {e}")
            raise
    
    def _initialize_embeddings(self):
        """Initialize the embedding model."""
        try:
            embedding_model_path = EMBEDDING_MODELS.get(
                self.embedding_model_name, 
                self.embedding_model_name
            )
            
            self.embeddings = HuggingFaceEmbeddings(
                model_name=embedding_model_path,
                model_kwargs={'device': settings.embedding_device}
            )
            logger.info(f"Successfully initialized embeddings: {embedding_model_path}")
        except Exception as e:
            logger.error(f"Failed to initialize embeddings: {e}")
            raise
    
    def _load_data(self):
        """Load trademark data from pickle file."""
        try:
            if os.path.exists(self.data_path):
                self.df = pd.read_pickle(self.data_path)
                logger.info(f"Loaded trademark data: {len(self.df)} records")
            else:
                logger.warning(f"Data file not found: {self.data_path}")
                self.df = pd.DataFrame()  # Empty dataframe as fallback
        except Exception as e:
            logger.error(f"Failed to load data: {e}")
            self.df = pd.DataFrame()
    
    def _setup_prompt(self):
        """Setup the prompt template for trademark analysis."""
        self.prompt = PromptTemplate.from_template("""
You are an expert trademark attorney AI assistant. Your task is to analyze trademark conflicts and provide professional legal opinions.

Given a trademark search query and retrieved similar trademarks, provide a comprehensive analysis including:

1. **Direct Matches**: List any exact or near-exact matches found
2. **Similar Trademarks**: Identify the top 5 most similar trademarks with their status
3. **Conflict Analysis**: For each similar trademark, explain:
   - Likelihood of confusion (visual, phonetic, conceptual similarity)
   - Goods/services overlap
   - Trademark strength and distinctiveness
   - Current status (LIVE, DEAD, CANCELLED, etc.)
4. **Legal Opinion**: Provide a clear recommendation on whether the trademark can be registered

**Important Legal Principles:**
- DEAD or CANCELLED trademarks generally do not create conflicts
- Consider the strength and fame of existing marks
- Analyze goods/services classification overlap
- Assess likelihood of consumer confusion

**Trademark Search Query**: {question}

**Retrieved Trademark Data**: {context}

**Professional Analysis**:
""")

    def filter_trademarks(self, df: pd.DataFrame, term: str) -> pd.DataFrame:
        """Filter trademarks based on search term with improved matching."""
        if df.empty:
            return df

        try:
            # Get stopwords for multiple languages
            stop_words = set()
            for lang in ['english', 'spanish', 'portuguese']:
                try:
                    stop_words.update(stopwords.words(lang))
                except:
                    pass

            # Clean and split the search term
            words = [word.strip() for word in term.split() if word.strip().lower() not in stop_words]

            if not words:
                return pd.DataFrame()

            # Escape special regex characters
            escaped_words = []
            for word in words:
                escaped_word = re.escape(word)
                escaped_words.append(escaped_word)

            # Create regex pattern for word boundaries
            pattern = '|'.join(fr'\b{word}\b' for word in escaped_words)

            # Filter the dataframe
            if 'trademarked_name' in df.columns:
                filtered_df = df[df['trademarked_name'].str.contains(
                    pattern, case=False, regex=True, na=False
                )]
            else:
                logger.warning("Column 'trademarked_name' not found in dataframe")
                return pd.DataFrame()

            logger.info(f"Filtered {len(filtered_df)} trademarks for term: {term}")
            return filtered_df

        except Exception as e:
            logger.error(f"Error filtering trademarks: {e}")
            return pd.DataFrame()

    def ingest_data(self, df: pd.DataFrame):
        """Ingest filtered trademark data into vector store."""
        if df.empty:
            logger.warning("No data to ingest")
            return

        try:
            # Convert DataFrame rows to text
            def format_row(row):
                formatted_parts = []
                for col, val in row.items():
                    if pd.notna(val) and str(val).strip():
                        formatted_parts.append(f"{col}: {val}")
                return " | ".join(formatted_parts)

            texts = df.apply(format_row, axis=1).tolist()

            # Create vector store
            self.vector_store = Chroma.from_texts(
                texts=texts,
                embedding=self.embeddings,
                persist_directory=settings.vector_store_persist_directory
            )

            # Setup retriever
            self.retriever = self.vector_store.as_retriever(
                search_kwargs={'k': settings.retrieval_k}
            )

            # Create the RAG chain
            self.chain = (
                {
                    "context": self.retriever,
                    "question": RunnablePassthrough()
                }
                | self.prompt
                | self.model
                | StrOutputParser()
            )

            logger.info(f"Successfully ingested {len(texts)} trademark records")

        except Exception as e:
            logger.error(f"Error ingesting data: {e}")
            raise

    def search_trademarks(self, term: str) -> str:
        """Main method to search for trademark conflicts."""
        try:
            if self.df is None or self.df.empty:
                return "❌ **Error**: Trademark database not loaded. Please ensure the data file exists."

            # Filter relevant trademarks
            filtered_df = self.filter_trademarks(self.df, term)

            if filtered_df.empty:
                return f"✅ **Good News!** No existing trademarks found matching '{term}'. This suggests the trademark may be available for registration, but we recommend conducting a comprehensive professional search."

            # Ingest filtered data
            self.ingest_data(filtered_df)

            if not self.chain:
                return "❌ **Error**: RAG system not properly initialized."

            # Get AI analysis
            ai_analysis = self.chain.invoke(term)

            # Format the response
            result = f"""
## 🔍 **Trademark Search Results for: "{term}"**

**Found {len(filtered_df)} potentially relevant trademarks in the database.**

{ai_analysis}

---
*⚠️ **Disclaimer**: This analysis is for informational purposes only and does not constitute legal advice. Please consult with a qualified trademark attorney for professional guidance.*
"""

            return result

        except Exception as e:
            logger.error(f"Error in trademark search: {e}")
            return f"❌ **Error**: An error occurred during the search: {str(e)}"

    def change_model(self, provider: str, model_name: str, **kwargs):
        """Change the language model on the fly."""
        try:
            self.model_provider = provider
            self.model_name = model_name
            self._initialize_model(**kwargs)

            # Rebuild chain if it exists
            if self.retriever:
                self.chain = (
                    {
                        "context": self.retriever,
                        "question": RunnablePassthrough()
                    }
                    | self.prompt
                    | self.model
                    | StrOutputParser()
                )

            logger.info(f"Successfully changed model to {provider}:{model_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to change model: {e}")
            return False

    def get_available_models(self) -> Dict[str, List[str]]:
        """Get list of available models by provider."""
        return {provider: list(models.keys()) for provider, models in MODEL_CONFIGS.items()}

    def clear_vector_store(self):
        """Clear the vector store and chain."""
        self.vector_store = None
        self.retriever = None
        self.chain = None
        logger.info("Vector store cleared")
